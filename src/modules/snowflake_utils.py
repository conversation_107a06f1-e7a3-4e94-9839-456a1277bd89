from contextlib import contextmanager
from snowflake.snowpark import Session, Row
from gestalt.utils.helpers_boto3 import get_parameter_value
import time

import logging

# Set up logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)

# Suppress INFO logs from the Snowflake Snowpark module
logging.getLogger("snowflake.snowpark").setLevel(logging.WARNING)

# Get credentials from AWS Parameter Store
@contextmanager
def snowflake_local_session(customer_key):
    """Context manager for Snowflake session using Snowpark."""
    # Define Snowflake connection configuration
    database_name = f"{customer_key}_DB"
    schema_name = f"{database_name}.EVENTS"
        
    # Define Snowflake connection configuration
    snowpark_config: dict[str, int | str] = {
        "account": get_parameter_value("/gestalt/client/snowflake/prod/credentials/account"),
        "user": f"{customer_key.upper()}_USER",
        "password": get_parameter_value("/gestalt/client/snowflake/prod/credentials/password"),
        "role": f"{customer_key.upper()}_ROLE",
        "warehouse": f"{customer_key.upper()}_WH_XSMALL",
        "database": database_name,
        "schema": schema_name
    }

    # Establish the Snowpark session
    session = Session.builder.configs(snowpark_config).create()

    try:
        yield session
    finally:
        session.close()

def execute_query(query, customer_key="GESTALT_CLIENT", params=None, max_retries=29, retry_delay=30) -> list[Row]:
    """
    Execute a query and return results using Snowpark with retry logic.

    By default, will retry every 30 seconds for 14.5 minutes.
    
    Args:
        query (str): The SQL query to execute.
        customer_key (str): The customer key for the session.
        params (list or tuple, optional): Parameters for the query.
        max_retries (int): Maximum number of retry attempts (default=29).
        retry_delay (int): Delay between retries in seconds (default=30).

    Returns:
        list: Query results.

    Raises:
        Exception: If all retries fail, the last exception is raised.
    """
    attempt: int = 0
    while attempt < max_retries:
        attempt += 1
        try:
            with snowflake_local_session(customer_key) as session:
                if params:
                    result: list[Row] = session.sql(query.format(*params)).collect()
                else:
                    result: list[Row] = session.sql(query).collect()
                logger.info(f"Query executed successfully on attempt {attempt}")
                return result
        except Exception as error:
            logger.error(f"Error executing query on attempt {attempt}: {error}")
            logger.info(f"Retrying in {retry_delay} seconds...")
            time.sleep(retry_delay)
    else:
        logger.info("Max retries reached: failed to insert event into Snowflake.")
        raise
