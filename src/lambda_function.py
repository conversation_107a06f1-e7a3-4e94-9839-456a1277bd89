"""Ingestion Engine Handler lambda function"""
import json
import logging
from typing import Any

import boto3

from modules.snowflake_utils import execute_query

logger: logging.Logger = logging.getLogger()
logger.setLevel(logging.INFO)

events_client = boto3.client('events')

def lambda_handler(event: dict[str, Any], context) -> dict[str, Any]:
    """
    AWS Lambda handler for processing EventBridge events related to S3 object ingestion.
    This function logs the received event, logs if it is a replay or live event, republishes
    the event to a custom EventBridge event bus, and inserts a record of the event into a Snowflake
    database with status tracking.

    Parameters
    ----------
    event : dict[str, Any]
        The event payload received from AWS EventBridge, expected to contain S3 bucket and object details.
    context : Any
        The Lambda context object (not used directly in this function).

    Returns
    -------
    dict
        A response dictionary with 'statusCode' and 'body' indicating the result of processing.

    Raises
    ------
    KeyError
        If the required 'detail' key is missing from the event payload.

    Logs
    ----
    - Received event details.
    - Whether the event is a replay or live event.
    - Details about the S3 bucket and object.
    - Republishing status to the custom EventBridge event bus.
    - SQL query for inserting the event into Snowflake.
    - Success or error messages for each processing step.
    """

    logger.info(f'Received Event: {json.dumps(event)}')
    
    if 'detail' not in event:
        raise KeyError('No configured keys found.')

    replay_name: str | None = event.get('replay-name')
    event_timestamp: str = event['time']
    eventbridge_event_id: str = event['id']
    bucket: str = event['detail']['bucket']['name']
    customer_key: str = bucket.replace('-','_')
    source_key: str = event['detail']['object']['key']
    status: str = 'Received'

    logger.info(f'Detected EventBridge Event for bucket: {bucket} - key: {source_key}')

    if replay_name is None:
        logger.info('This is a live event')
    else:
        logger.info(f'This event is part of a replay: {replay_name}')
    
    # Publish the Received Event back to the Custom Event Bus & Include the Original Event ID
    republish_event_entry: dict[str, Any] = {
        'Source': 'gestalt-ingestion-engine-handler.processed.events',
        'DetailType': 'Processed Event for Gestalt Ingestion Processor',
        'Detail': json.dumps({
            'original_event_id': event['id'],
            'bucket': event['detail']['bucket'],
            'object': event['detail']['object']
        }),
        'EventBusName': 'gestalt-ingestion-processor-event-bus'
    }
    logger.info(f'Republishing event {republish_event_entry}')
    try:
        response: dict[str, Any] = events_client.put_events(Entries=[republish_event_entry])
        logger.info(f'Republish response: {response}')
    except events_client.exceptions.InternalException as error:
        logger.error(f'Error republishing event: {error}')
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error republishing event {eventbridge_event_id}: {error}')
        }
    
    # Insert the event into Snowflake with status
    insert_query: str = f"""
        INSERT INTO EVENTS.INGESTION_ENGINE__PROCESSING_EVENTS
        (id_event, id_customer, text_bucket, text_land_key, timestamp_event_received, status_event)
        VALUES ('{eventbridge_event_id}', '{customer_key}', '{bucket}', '{source_key}', '{event_timestamp}', '{status}')
    """
    logger.info(f'Insert query to execute: {json.dumps(" ".join(insert_query.split()))}')
    try: 
        execute_query(insert_query)
        logger.info(f"Stored event {eventbridge_event_id} from {bucket}/{source_key} in Snowflake with status '{status}'")
    except Exception as error:
        logger.error(f'Error inserting event {eventbridge_event_id} into Snowflake: {error}')
        return {
            'statusCode': 500,
            'body': json.dumps(f'Error processing event {eventbridge_event_id}')
        }

    # Return success
    return {
        'statusCode': 200,
        'body': json.dumps('Record Processed and Status Updated in Snowflake')
    }
