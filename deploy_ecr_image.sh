#!/bin/bash

set -euo pipefail  # Exit on error, unset variable usage, or pipeline failure

# Variables
REGION="us-east-2"
ACCOUNT_ID="************"
REPO_NAME="gestalt-ingestion-engine-handler-docker-image"
FUNCTION_NAME="gestalt-ingestion-engine-handler"
ROLE_ARN="arn:aws:iam::************:role/service-role/gestalt-ingestion-processor-role-nfth9jra"

# Verify GITHUB_TOKEN is set
if [ -z "${GITHUB_TOKEN:-}" ]; then
    echo "Error: GITHUB_TOKEN is not set."
    exit 1
fi

# Generate a version tag based on the Git commit hash or timestamp
VERSION=$(git rev-parse --short HEAD || date +%Y%m%d%H%M%S)
IMAGE_URI="$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/$REPO_NAME"

# Step 1: Check if the ECR repository exists, and create it if necessary
if aws ecr describe-repositories --repository-names $REPO_NAME --region $REGION > /dev/null 2>&1; then
    echo "ECR repository $REPO_NAME already exists."
else
    echo "Creating ECR repository $REPO_NAME..."
    aws ecr create-repository --repository-name $REPO_NAME --region $REGION
fi

# Step 2: Authenticate Docker to Amazon ECR
aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com

# Step 3: Build the Docker Image
docker build --provenance=false --no-cache --platform linux/amd64 --build-arg GITHUB_TOKEN="${GITHUB_TOKEN}" -t $REPO_NAME:latest -t $REPO_NAME:"$VERSION" .

# Step 4: Push the Docker Image to ECR
docker tag $REPO_NAME:latest $IMAGE_URI:latest
docker tag $REPO_NAME:"$VERSION" $IMAGE_URI:"$VERSION"
docker push $IMAGE_URI:latest
docker push $IMAGE_URI:"$VERSION"

# Step 5: Retrieve platform-specific image digest
# shellcheck disable=SC2016
IMAGE_DIGEST=$(aws ecr describe-images \
    --repository-name $REPO_NAME \
    --region $REGION \
    --query 'reverse(sort_by(imageDetails[?imageSizeInBytes > `2000` && imageManifestMediaType==`application/vnd.oci.image.manifest.v1+json`], &imagePushedAt))[:1].imageDigest' \
    --output text)

if [ -z "$IMAGE_DIGEST" ]; then
    echo "Error: Failed to retrieve the image digest for $REPO_NAME:$VERSION"
    exit 1
fi

IMAGE_URI_WITH_DIGEST="$IMAGE_URI@$IMAGE_DIGEST"
echo "Retrieved Image URI with digest: $IMAGE_URI_WITH_DIGEST"

# Step 6: Create or Update the Lambda Function
if aws lambda get-function --function-name $FUNCTION_NAME > /dev/null 2>&1; then
    echo "Lambda function $FUNCTION_NAME exists. Updating..."
    aws lambda update-function-code \
        --function-name $FUNCTION_NAME \
        --image-uri "$IMAGE_URI_WITH_DIGEST"
else
    echo "Creating Lambda function $FUNCTION_NAME..."
    aws lambda create-function \
        --function-name $FUNCTION_NAME \
        --package-type Image \
        --code ImageUri="$IMAGE_URI_WITH_DIGEST" \
        --role $ROLE_ARN \
        --description "Gestalt Ingestion Engine Handler" \
        --timeout 900 \
        --memory-size 512
fi
