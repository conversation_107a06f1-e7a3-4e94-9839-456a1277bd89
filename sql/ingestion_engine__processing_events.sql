create or replace table EVENTS.INGESTION_ENGINE__PROCESSING_EVENTS (
    ID_EVENT STRING primary key                           -- EVE<PERSON><PERSON><PERSON><PERSON> IDENTIFIER FOR THE EVENT
    , ID_CUSTOMER STRING not null                         -- CUSTOMER-<PERSON>ECIF<PERSON> IDENTIFIER
    , TIMESTAMP_EVENT_RECEIVED TIMESTAMP_TZ default current_timestamp -- TIMESTAMP WHEN THE EVENT IS RECEIVED
    , TIMESTAMP_EVENT_PROCESSED TIMESTAMP_TZ              -- TIMESTAMP WHEN THE EVENT IS PROCESSED
    , TEXT_BUCKET STRING not null                         -- BUCKET NAME WHERE THE FILE IS STORED
    , TEXT_LAND_KEY STRING                                -- PATH OR KEY IN THE BUCKET FOR THE LANDING FILE
    , TEXT_BUILD_KEY STRING                               -- PATH OR KEY FOR THE BUILD FILE
    , TEXT_JSONL_KEY STRING                               -- PATH OR KEY FOR THE JSONL FILE
    , TEXT_EVENT_REPLAY_NAME STRING                       -- NAME OR IDENTIFIER FOR EVENT REPLAY
    , STATUS_EVENT STRING default 'Received'              -- STATUS OF THE EVENT (E.G., RECEIVED, PROCESSED)
);
