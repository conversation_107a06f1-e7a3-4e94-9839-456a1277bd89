{"cells": [{"cell_type": "code", "execution_count": 20, "id": "6597917a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Attempt 1, trying again after 30 seconds; total time: 30 (0.50 minutes)\n", "Attempt 2, trying again after 30 seconds; total time: 60 (1.00 minutes)\n", "Attempt 3, trying again after 30 seconds; total time: 90 (1.50 minutes)\n", "Attempt 4, trying again after 30 seconds; total time: 120 (2.00 minutes)\n", "Attempt 5, trying again after 30 seconds; total time: 150 (2.50 minutes)\n", "Attempt 6, trying again after 30 seconds; total time: 180 (3.00 minutes)\n", "Attempt 7, trying again after 30 seconds; total time: 210 (3.50 minutes)\n", "Attempt 8, trying again after 30 seconds; total time: 240 (4.00 minutes)\n", "Attempt 9, trying again after 30 seconds; total time: 270 (4.50 minutes)\n", "Attempt 10, trying again after 30 seconds; total time: 300 (5.00 minutes)\n", "Attempt 11, trying again after 30 seconds; total time: 330 (5.50 minutes)\n", "Attempt 12, trying again after 30 seconds; total time: 360 (6.00 minutes)\n", "Attempt 13, trying again after 30 seconds; total time: 390 (6.50 minutes)\n", "Attempt 14, trying again after 30 seconds; total time: 420 (7.00 minutes)\n", "Attempt 15, trying again after 30 seconds; total time: 450 (7.50 minutes)\n", "Attempt 16, trying again after 30 seconds; total time: 480 (8.00 minutes)\n", "Attempt 17, trying again after 30 seconds; total time: 510 (8.50 minutes)\n", "Attempt 18, trying again after 30 seconds; total time: 540 (9.00 minutes)\n", "Attempt 19, trying again after 30 seconds; total time: 570 (9.50 minutes)\n", "Attempt 20, trying again after 30 seconds; total time: 600 (10.00 minutes)\n", "Attempt 21, trying again after 30 seconds; total time: 630 (10.50 minutes)\n", "Attempt 22, trying again after 30 seconds; total time: 660 (11.00 minutes)\n", "Attempt 23, trying again after 30 seconds; total time: 690 (11.50 minutes)\n", "Attempt 24, trying again after 30 seconds; total time: 720 (12.00 minutes)\n", "Attempt 25, trying again after 30 seconds; total time: 750 (12.50 minutes)\n", "Attempt 26, trying again after 30 seconds; total time: 780 (13.00 minutes)\n", "At<PERSON>pt 27, trying again after 30 seconds; total time: 810 (13.50 minutes)\n", "Attempt 28, trying again after 30 seconds; total time: 840 (14.00 minutes)\n", "At<PERSON>pt 29, trying again after 30 seconds; total time: 870 (14.50 minutes)\n", "Max retries reached\n"]}], "source": ["max_retries: int = 29\n", "retry_delay: int = 30\n", "total_time: int = 0\n", "attempt: int = 0\n", "\n", "while attempt < max_retries:\n", "    attempt += 1\n", "    # if attempt > 20: break # Simulate success  # noqa: E701\n", "    total_time += retry_delay\n", "    print(f\"Attempt {attempt}, trying again after {retry_delay} seconds; total time: {total_time} ({(total_time / 60):.2f} minutes)\")\n", "else:\n", "    print(\"Max retries reached\")\n"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}