# Stage 1: Build and install dependencies
FROM --platform=linux/amd64 public.ecr.aws/lambda/python:3.11 AS builder

# Install Git and other necessary tools
RUN yum install -y git && yum clean all

# Set the GITHUB_TOKEN as a build argument and environment variable
ARG GITHUB_TOKEN
ENV GITHUB_TOKEN=${GITHUB_TOKEN}

# Copy the requirements file first to leverage Docker cache
COPY requirements.txt . ${LAMBDA_TASK_ROOT}

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the source code
COPY src/ ${LAMBDA_TASK_ROOT}

# Stage 2: Prepare the final runtime image
FROM --platform=linux/amd64 public.ecr.aws/lambda/python:3.11
RUN yum -y update expat && yum clean all

# Copy the site-packages directory from the builder stage
COPY --from=builder /var/lang/lib/python3.11/site-packages /var/lang/lib/python3.11/site-packages

# Copy only the necessary files from the builder stage
COPY --from=builder ${LAMBDA_TASK_ROOT} ${LAMBDA_TASK_ROOT}

# Set the command to run the Lambda function
CMD ["lambda_function.lambda_handler"]
