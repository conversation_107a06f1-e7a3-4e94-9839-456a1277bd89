
Gestalt Ingestion Event Handler
===============================

This repository contains the code and instructions for developing, testing, and deploying the "gestalt-ingestion-event-handler" AWS Lambda function.

Generating PAT token
---------------------

We need a PAT token to install one of the dependencies `gestalt-core-tools` as it is hosted in a private repo.
Step 1: Log in to GitHub
Open your web browser and go to GitHub.
Log in with your GitHub account credentials (username and password).

Step 2: Go to Personal Settings
In the upper-right corner of any GitHub page, click your profile picture.
In the dropdown menu, select Settings.

Step 3: Access Developer Settings
In the left sidebar, scroll down and find the Developer settings section.
Click on Developer settings.

Step 4: Create a New Personal Access Token
On the left sidebar, click Personal access tokens.
Click the Generate new token button.

Step 5: Configure Your Token
Give your token a descriptive name in the Note field (for example, "My GitHub PAT").
Set an Expiration date (optional). By default, the token does not expire, but setting a shorter expiration is a good security practice.
Under Resource Owner, select `Gestalt-Tech-Inc`
Under Repository Access, select `gestalt-core-tools`
Under Repository Permissions, select `Read-Only` in Contents
Scroll down and click Generate token.

**Note: After generating the token, you need to wait for it to be approved.**

Step 6: Save the Token
After generating the token, GitHub will display it only once. Make sure to copy it immediately.
Store the token securely, such as in a password manager.
Would recommend to store it as env variable with name 'GITHUB_TOKEN'.

Develop/Test Lambda Locally
---------------------------

Step 1: Clone the Repository
Clone the latest version of the code from the repository:

    <NAME_EMAIL>:Gestalt-Tech-Inc/gestalt-ingestion-processor.git
    cd gestalt_ingestion_processor

Step 2: Choose Step 2a or Step 2b below

Step 2a: Set Up the Python Virtual Environment
Using `uv`, create the Python virtual environment:

    # Install Python, if necessary, and create a virtual environment
    uv sync

    # Activate environment, if necessary
    source .venv/bin/activate || .venv\Scripts\activate

Step 2b: Set Up the Conda Environment
From the root directory "gestalt_ingestion_processor", create and activate the Conda environment:

    # Create the Conda environment
    conda env create --file conda-local-test-env.yml

    # Activate the environment
    conda activate gestalt-ingestion-processor

Step 3: Execute Tests
To run the tests:

1. Change directory to the "tests" folder:
       cd tests

2. Execute the test scripts:
       python3.11 test_lambda_function_handler.py

Note: The tests are designed to use either sample files from the local directory or dummy data embedded in the code. Ensure that the code is updated with the appropriate test data before executing the tests.

Deploy
------

Step 1: Navigate to the Root Directory
Change to the root directory "gestalt-ingestion-event-handler":

    cd gestalt-ingestion-event-handler

Step 2: Execute the Deployment Script
Run the deployment script:

    bash deploy_ecr_image.sh

Step 3: Verify Deployment
Once the script completes successfully, it will create a new ECR image and create/update the Lambda function.

Step 4: Update Configuration
Finally, update the Lambda function's configuration:

    Timeout: Set the timeout to 15 minutes.

Conclusion
----------

By following these steps, you can develop, test, and deploy the "gestalt-ingestion-event-handler" Lambda function efficiently. Make sure to verify all configurations and dependencies are correctly set up to ensure smooth operation.
