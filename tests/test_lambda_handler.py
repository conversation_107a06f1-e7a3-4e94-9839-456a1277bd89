import sys
import os
import json

sys.path.append(os.path.join(os.path.dirname(__file__), '../src'))
from lambda_function import lambda_handler

def test_lambda_handler():
    event = {
                    'version': '0', 
                    'id': '93c5ad2a-eada-9169-3b01-9da1528b6edf', 
                    'detail-type': 'Object Created', 
                    'source': 'aws.s3', 
                    'account': '************', 
                    'time': '2024-08-27T05:02:49Z', 
                    'region': 'us-east-2', 
                    'resources': ['arn:aws:s3:::gestalt-eventbridge-s3-dev'], 
                    'detail': 
                    {'version': '0', 
                               'bucket': {'name': 'gestalt-eventbridge-s3-dev'}, 
                               'object': {'key': 'partners/defilos/land/455664_Always_20240827120240.xml', 'size': 148073, 'etag': '3b518f26f3f8ab5315216bb2ebd1b5a2', 'sequencer': '0066CD5DF90A95FA29'}, 
                               'request-id': 'QKB42C9EKM7APKCY', 'requester': '************', 'source-ip-address': '**************', 'reason': 'PutObject'
                    }
                 }

    result = lambda_handler(event, None)
    print("\nFollowng is the response from Lamda....")
    print(result)

if __name__ == "__main__":
    test_lambda_handler()